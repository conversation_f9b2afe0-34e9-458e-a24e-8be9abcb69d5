import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { transferService } from '../../services/transferService';

const TransferDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transfer, setTransfer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTransferDetail();
  }, [id]);

  const fetchTransferDetail = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await transferService.getTransferById(id);
      
      if (response.success) {
        setTransfer(response.transfer);
      } else {
        setError('Transfer bulunamadı');
      }
    } catch (error) {
      console.error('Transfer detayı yüklenirken hata:', error);
      setError('Transfer detayı yüklenirken bir hata oluştu');
      toast.error('Transfer detayı yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return '-';
    return timeString.substring(0, 5); // HH:MM formatına çevir
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'assigned':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'in_progress':
        return 'Devam Ediyor';
      case 'assigned':
        return 'Atandı';
      case 'pending':
        return 'Beklemede';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">{error}</div>
          <button
            onClick={() => navigate('/operations/transfers')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Transferlere Dön
          </button>
        </div>
      </div>
    );
  }

  if (!transfer) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600 text-xl mb-4">Transfer bulunamadı</div>
          <button
            onClick={() => navigate('/operations/transfers')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Transferlere Dön
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <button
              onClick={() => navigate('/operations/transfers')}
              className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Transferlere Dön
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              Transfer Detayı
            </h1>
            <p className="text-gray-600">
              {transfer.is_return ? 'Dönüş Transferi' : 'Varış Transferi'} - {transfer.reservation?.reservation_number}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(transfer.status)}`}>
              {getStatusText(transfer.status)}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Ana Bilgiler */}
        <div className="lg:col-span-2 space-y-6">
          {/* Transfer Bilgileri */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Transfer Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Transfer Tarihi</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(transfer.transfer_date)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Transfer Saati</label>
                <p className="mt-1 text-sm text-gray-900">{formatTime(transfer.transfer_time)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Uçuş Numarası</label>
                <p className="mt-1 text-sm text-gray-900">{transfer.flight_number || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Oda Numarası</label>
                <p className="mt-1 text-sm text-gray-900">{transfer.room_number || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Bebek Koltuğu</label>
                <p className="mt-1 text-sm text-gray-900">{transfer.baby_seat_count || 0}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Çocuk Koltuğu</label>
                <p className="mt-1 text-sm text-gray-900">{transfer.child_seat_count || 0}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Yükseltici Koltuk</label>
                <p className="mt-1 text-sm text-gray-900">{transfer.booster_count || 0}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Nakit Ödeme</label>
                <p className="mt-1 text-sm text-gray-900">
                  {transfer.cash_payment} {transfer.cash_currency}
                </p>
              </div>
            </div>
          </div>

          {/* Rezervasyon Bilgileri */}
          {transfer.reservation && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Rezervasyon Bilgileri</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Rezervasyon Numarası</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.reservation_number}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Acente Referansı</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.agency_reference || '-'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Acente</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.agency?.name || '-'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Yolcu Sayısı</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.passenger_count}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Nereden</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.fromRegion?.name || '-'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Nereye</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.reservation.toRegion?.name || '-'}</p>
                </div>
              </div>
            </div>
          )}

          {/* Notlar */}
          {(transfer.driver_note || transfer.operation_note) && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Notlar</h2>
              <div className="space-y-4">
                {transfer.driver_note && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Şoför Notu</label>
                    <p className="mt-1 text-sm text-gray-900">{transfer.driver_note}</p>
                  </div>
                )}
                {transfer.operation_note && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Operasyon Notu</label>
                    <p className="mt-1 text-sm text-gray-900">{transfer.operation_note}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Yan Panel */}
        <div className="space-y-6">
          {/* Araç Bilgileri */}
          {transfer.vehicle && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Araç Bilgileri</h2>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Plaka</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.vehicle.plate_number}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Araç Tipi</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.vehicle.vehicleType?.name || '-'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Kapasite</label>
                  <p className="mt-1 text-sm text-gray-900">{transfer.vehicle.capacity || '-'}</p>
                </div>
              </div>
            </div>
          )}

          {/* İşlemler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">İşlemler</h2>
            <div className="space-y-3">
              <button
                onClick={() => navigate(`/operations/transfers/${id}/edit`)}
                className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
              >
                Düzenle
              </button>
              <button
                onClick={() => navigate(`/reservations/${transfer.reservation_id}`)}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                Rezervasyonu Görüntüle
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferDetail;
