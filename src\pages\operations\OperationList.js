import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
// import { reservationService } from '../../services/reservationService';
import { transferService } from '../../services/transferService';
import { vehicleService } from '../../services/vehicleService';
import { carrierService } from '../../services/carrierService';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  TruckIcon,
  EyeIcon,
  PencilSquareIcon
} from '@heroicons/react/24/outline';

const OperationList = () => {
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [vehicles, setVehicles] = useState([]);
  const [carriers, setCarriers] = useState([]);
  const [transferVehicles, setTransferVehicles] = useState({});
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    date: '',
    vehicleId: '',
    carrierId: ''
  });

  // NOT: Transfer atama modalı için state kaldırıldı

  useEffect(() => {
    fetchTransfers();
    fetchVehicles();
    fetchCarriers();
  }, [page, filters]);

  // NOT: Taşıyıcı firma seçildiğinde araçlarını getir fonksiyonu kaldırıldı
  // Artık her transfer için ayrı araç listesi tutuyoruz

  const fetchTransfers = async () => {
    setLoading(true);
    try {
      const response = await transferService.getAllTransfers({
        page,
        limit: 10,
        status: filters.status,
        date: filters.date,
        vehicleId: filters.vehicleId
      });

      if (response.success) {
        setTransfers(response.data.transfers);
        setTotalPages(response.data.pagination.totalPages);
      } else {
        toast.error('Transferler yüklenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error fetching transfers:', error);
      toast.error('Transferler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const fetchVehicles = async () => {
    try {
      const response = await vehicleService.getVehicles();
      if (response.success) {
        // API'den gelen verileri frontend formatına dönüştür
        const formattedVehicles = response.data.rows.map(vehicle => ({
          ...vehicle,
          plateNumber: vehicle.plate_number,
          vehicleType: vehicle.vehicleType || {
            id: vehicle.vehicle_type_id,
            name: vehicle.vehicle_type?.name || 'Bilinmiyor'
          }
        }));
        setVehicles(formattedVehicles);
      }
    } catch (error) {
      console.error('Error fetching vehicles:', error);
    }
  };

  const fetchCarriers = async () => {
    try {
      const response = await carrierService.getCarriers();
      if (response.success) {
        setCarriers(response.data.rows);
      }
    } catch (error) {
      console.error('Error fetching carriers:', error);
    }
  };

  // Belirli bir taşıyıcı firmaya ait araçları getir
  const fetchCarrierVehicles = async (carrierId) => {
    try {
      const response = await carrierService.getCarrierVehicles(carrierId);
      if (response.success) {
        // API'den gelen verileri frontend formatına dönüştür
        const formattedVehicles = response.data.rows.map(vehicle => ({
          ...vehicle,
          plateNumber: vehicle.plate_number,
          vehicleType: vehicle.vehicleType || {
            id: vehicle.vehicle_type_id,
            name: vehicle.vehicle_type?.name || 'Bilinmiyor'
          }
        }));
        return formattedVehicles;
      }
      return [];
    } catch (error) {
      console.error('Error fetching carrier vehicles:', error);
      return [];
    }
  };

  // NOT: Transfer için taşıyıcı firma seçme modalı kaldırıldı

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setPage(1); // Filtre değiştiğinde ilk sayfaya dön
  };

  // Taşıyıcı firma seçildiğinde
  const handleCarrierChange = async (e, transferId) => {
    const carrierId = e.target.value;
    if (!carrierId) return;

    try {
      // Taşıyıcı firmaya ait araçları getir
      const vehicles = await fetchCarrierVehicles(carrierId);

      // Transfer için araç listesini güncelle
      setTransferVehicles(prev => ({
        ...prev,
        [transferId]: vehicles
      }));

      // Transfer için taşıyıcı firma ataması yap
      const response = await transferService.assignCarrier(transferId, carrierId);

      if (response.success) {
        // Transferi güncelle
        setTransfers(prevTransfers =>
          prevTransfers.map(t =>
            t.id === transferId ? { ...t, carrier: response.transfer.carrier } : t
          )
        );

        toast.success('Taşıyıcı firma başarıyla atandı');
      }
    } catch (error) {
      console.error('Error assigning carrier:', error);
      toast.error('Taşıyıcı firma atanırken bir hata oluştu');
    }
  };

  // Araç atama
  const handleAssignVehicle = async (transferId, vehicleId) => {
    try {
      const response = await transferService.assignVehicle(transferId, vehicleId);
      if (response.success) {
        // Transferi güncelle
        setTransfers(prevTransfers =>
          prevTransfers.map(t =>
            t.id === transferId ? { ...t, vehicle: response.transfer.vehicle } : t
          )
        );

        toast.success('Araç başarıyla atandı');
      } else {
        toast.error(response.message || 'Araç atanırken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error assigning vehicle:', error);
      toast.error('Araç atanırken bir hata oluştu');
    }
  };

  const handleUpdateStatus = async (transferId, status) => {
    try {
      const response = await transferService.updateStatus(transferId, status);
      if (response.success) {
        // Transferi güncelle
        setTransfers(prevTransfers =>
          prevTransfers.map(t =>
            t.id === transferId ? { ...t, status: response.transfer.status } : t
          )
        );

        toast.success('Durum başarıyla güncellendi');
      } else {
        toast.error(response.message || 'Durum güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Durum güncellenirken bir hata oluştu');
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Bekliyor</span>;
      case 'confirmed':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Onaylandı</span>;
      case 'in_progress':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">Devam Ediyor</span>;
      case 'completed':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Tamamlandı</span>;
      case 'cancelled':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">İptal Edildi</span>;
      case 'no_show':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Gelmeyen</span>;
      default:
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">{status}</span>;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'dd MMMM yyyy', { locale: tr });
  };

  const formatTime = (timeString) => {
    if (!timeString) return '';
    return timeString;
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Operasyon Yönetimi</h1>
      </div>

      {/* Navigation Cards */}
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <Link
          to="/operations/transfers"
          className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
        >
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TruckIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Transferler</dt>
                  <dd className="text-lg font-medium text-gray-900">Tüm transfer işlemleri</dd>
                </dl>
              </div>
            </div>
          </div>
        </Link>

        <div className="bg-white overflow-hidden shadow rounded-lg opacity-50">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Günlük Planlama</dt>
                  <dd className="text-lg font-medium text-gray-900">Yakında...</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg opacity-50">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-6 w-6 text-gray-400" aria-hidden="true" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Raporlar</dt>
                  <dd className="text-lg font-medium text-gray-900">Yakında...</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filtreler */}
      <div className="mt-4 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-2">
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Durum
            </label>
            <select
              id="status"
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
            >
              <option value="">Tümü</option>
              <option value="pending">Bekliyor</option>
              <option value="confirmed">Onaylandı</option>
              <option value="in_progress">Devam Ediyor</option>
              <option value="completed">Tamamlandı</option>
              <option value="cancelled">İptal Edildi</option>
              <option value="no_show">Gelmeyen</option>
            </select>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="date" className="block text-sm font-medium text-gray-700">
              Tarih
            </label>
            <input
              type="date"
              name="date"
              id="date"
              value={filters.date}
              onChange={handleFilterChange}
              className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="vehicleId" className="block text-sm font-medium text-gray-700">
              Araç
            </label>
            <select
              id="vehicleId"
              name="vehicleId"
              value={filters.vehicleId}
              onChange={handleFilterChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
            >
              <option value="">Tümü</option>
              {vehicles.map(vehicle => (
                <option key={vehicle.id} value={vehicle.id}>
                  {vehicle.plateNumber} - {vehicle.vehicleType?.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Transfer Listesi */}
      <div className="mt-8 flex flex-col">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rezervasyon
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Yön
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tarih / Saat
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Durum
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Araç
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan="6" className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                        Yükleniyor...
                      </td>
                    </tr>
                  ) : transfers.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                        Kayıt bulunamadı
                      </td>
                    </tr>
                  ) : (
                    transfers.map(transfer => (
                      <tr key={transfer.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {transfer.reservation?.reservation_number}
                          </div>
                          <div className="text-sm text-gray-500">
                            {transfer.reservation?.agency?.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {transfer.isReturn ? 'Dönüş' : 'Gidiş'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {transfer.reservation?.fromRegion?.name} → {transfer.reservation?.toRegion?.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatDate(transfer.transferDate)}</div>
                          <div className="text-sm text-gray-500">{formatTime(transfer.transferTime)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(transfer.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {transfer.vehicle ? (
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {transfer.vehicle.plateNumber}
                              </div>
                              <div className="text-sm text-gray-500">
                                {transfer.vehicle.vehicleType?.name}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500">Atanmadı</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {/* Taşıyıcı Firma Atama */}
                            <div className="relative mb-2">
                              <select
                                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                                onChange={(e) => handleCarrierChange(e, transfer.id)}
                                value={transfer.carrier?.id || ''}
                              >
                                <option value="">Taşıyıcı Firma Seç</option>
                                {carriers.map(carrier => (
                                  <option key={carrier.id} value={carrier.id}>
                                    {carrier.name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* Araç Atama */}
                            <div className="relative">
                              <select
                                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                                onChange={(e) => handleAssignVehicle(transfer.id, e.target.value)}
                                value={transfer.vehicle?.id || ''}
                                disabled={!transfer.carrier}
                              >
                                <option value="">Araç Seç</option>
                                {(transfer.carrier && transferVehicles[transfer.id] ? transferVehicles[transfer.id] : vehicles).map(vehicle => (
                                  <option key={vehicle.id} value={vehicle.id}>
                                    {vehicle.plate_number || vehicle.plateNumber} - {(vehicle.vehicleType?.name || vehicle.vehicle_type?.name || 'Bilinmiyor')}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* Durum Güncelleme */}
                            <div className="flex space-x-1">
                              <button
                                onClick={() => handleUpdateStatus(transfer.id, 'confirmed')}
                                className="text-blue-600 hover:text-blue-900"
                                title="Onayla"
                              >
                                <CheckCircleIcon className="h-5 w-5" />
                              </button>
                              <button
                                onClick={() => handleUpdateStatus(transfer.id, 'in_progress')}
                                className="text-purple-600 hover:text-purple-900"
                                title="Devam Ediyor"
                              >
                                <ClockIcon className="h-5 w-5" />
                              </button>
                              <button
                                onClick={() => handleUpdateStatus(transfer.id, 'completed')}
                                className="text-green-600 hover:text-green-900"
                                title="Tamamlandı"
                              >
                                <TruckIcon className="h-5 w-5" />
                              </button>
                              <button
                                onClick={() => handleUpdateStatus(transfer.id, 'cancelled')}
                                className="text-red-600 hover:text-red-900"
                                title="İptal Et"
                              >
                                <XCircleIcon className="h-5 w-5" />
                              </button>
                            </div>

                            {/* Detay ve Düzenleme */}
                            <Link
                              to={`/reservations/${transfer.reservation?.id}`}
                              className="text-primary-600 hover:text-primary-900"
                              title="Detay"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </Link>
                            <Link
                              to={`/operations/transfers/${transfer.id}/edit`}
                              className="text-primary-600 hover:text-primary-900"
                              title="Düzenle"
                            >
                              <PencilSquareIcon className="h-5 w-5" />
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Sayfalama */}
      <div className="mt-4 flex justify-between">
        <button
          onClick={() => setPage(prev => Math.max(prev - 1, 1))}
          disabled={page === 1}
          className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
            page === 1 ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          Önceki
        </button>
        <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
          Sayfa {page} / {totalPages}
        </span>
        <button
          onClick={() => setPage(prev => (prev < totalPages ? prev + 1 : prev))}
          disabled={page >= totalPages}
          className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
            page >= totalPages ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          Sonraki
        </button>
      </div>
    </div>
  );
};

export default OperationList;
