import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { reservationService } from '../../services/reservationService';
import { agencyService } from '../../services/agencyService';
import { regionService } from '../../services/regionService';
import { vehicleService } from '../../services/vehicleService';
import ReservationBasicInfo from './components/ReservationBasicInfo';
import PassengerInfo from './components/PassengerInfo';
import TransferInfo from './components/TransferInfo';
import ContactInfo from './components/ContactInfo';
import PriceSummary from './components/PriceSummary';
import StepProgressBar from './components/StepProgressBar';

const ReservationEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Dropdown verileri
  const [agencies, setAgencies] = useState([]);
  const [regions, setRegions] = useState([]);
  const [vehicleTypes, setVehicleTypes] = useState([]);

  // Form verileri
  const [formData, setFormData] = useState({
    // Temel bilgiler
    agencyId: '',
    agencyReference: '',
    fromRegionId: '',
    toRegionId: '',
    hotelName: '',
    secondaryHotelName: '',
    isRoundTrip: false,
    vehicleTypeId: '',
    price: '',
    currency: 'EUR',
    passengerCount: 1,

    // Yolcu bilgileri
    passengers: [
      {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        nationality: '',
        identityNumber: '',
        birthDate: '',
        gender: '',
        specialRequests: ''
      }
    ],

    // Transfer bilgileri
    transfers: [
      {
        direction: 'arrival', // arrival veya departure
        date: '',
        time: '',
        flightNumber: '',
        pickupLocation: '',
        dropoffLocation: '',
        notes: ''
      }
    ],

    // İletişim bilgileri
    contactDetails: [
      {
        type: 'email',
        value: '',
        isPrimary: true
      },
      {
        type: 'phone',
        value: '',
        isPrimary: true
      }
    ]
  });

  const steps = [
    { id: 1, name: 'Temel Bilgiler', description: 'Rezervasyon temel bilgileri' },
    { id: 2, name: 'Yolcu Bilgileri', description: 'Yolcu detayları' },
    { id: 3, name: 'Transfer Bilgileri', description: 'Transfer detayları' },
    { id: 4, name: 'İletişim Bilgileri', description: 'İletişim detayları' },
    { id: 5, name: 'Özet', description: 'Rezervasyon özeti' }
  ];

  useEffect(() => {
    loadInitialData();
  }, [id]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      
      // Paralel olarak tüm verileri yükle
      const [reservationResponse, agenciesResponse, regionsResponse, vehicleTypesResponse] = await Promise.all([
        reservationService.getReservationById(id),
        agencyService.getAgencies(),
        regionService.getRegions(),
        vehicleService.getVehicleTypes()
      ]);

      // Dropdown verilerini set et
      if (agenciesResponse?.success) {
        setAgencies(agenciesResponse.data?.agencies || []);
      }
      if (regionsResponse?.success) {
        setRegions(regionsResponse.data?.regions || []);
      }
      if (vehicleTypesResponse?.success) {
        setVehicleTypes(vehicleTypesResponse.data?.vehicleTypes || []);
      }

      // Rezervasyon verilerini form'a yükle
      if (reservationResponse?.success && reservationResponse.reservation) {
        const reservation = reservationResponse.reservation;
        
        setFormData({
          agencyId: reservation.agency_id || '',
          agencyReference: reservation.agency_reference || '',
          fromRegionId: reservation.from_region_id || '',
          toRegionId: reservation.to_region_id || '',
          hotelName: reservation.hotel_name || '',
          secondaryHotelName: reservation.secondary_hotel_name || '',
          isRoundTrip: reservation.is_round_trip || false,
          vehicleTypeId: reservation.vehicle_type_id || '',
          price: reservation.price || '',
          currency: reservation.currency || 'EUR',
          passengerCount: reservation.passenger_count || 1,

          // Yolcu bilgileri
          passengers: reservation.passengers?.map(passenger => ({
            firstName: passenger.first_name || '',
            lastName: passenger.last_name || '',
            email: passenger.email || '',
            phone: passenger.phone || '',
            nationality: passenger.nationality || '',
            identityNumber: passenger.identity_number || '',
            birthDate: passenger.birth_date || '',
            gender: passenger.gender || '',
            specialRequests: passenger.special_requests || ''
          })) || [
            {
              firstName: '',
              lastName: '',
              email: '',
              phone: '',
              nationality: '',
              identityNumber: '',
              birthDate: '',
              gender: '',
              specialRequests: ''
            }
          ],

          // Transfer bilgileri
          transfers: reservation.transfers?.map(transfer => ({
            direction: transfer.is_return ? 'departure' : 'arrival',
            date: transfer.transfer_date || '',
            time: transfer.transfer_time ? transfer.transfer_time.substring(0, 5) : '', // HH:MM formatına çevir
            flightNumber: transfer.flight_number || '',
            pickupLocation: transfer.pickup_location || transfer.room_number || '',
            dropoffLocation: transfer.dropoff_location || '',
            notes: transfer.driver_note || transfer.operation_note || ''
          })) || [
            {
              direction: 'arrival',
              date: '',
              time: '',
              flightNumber: '',
              pickupLocation: '',
              dropoffLocation: '',
              notes: ''
            }
          ],

          // İletişim bilgileri
          contactDetails: (() => {
            const contacts = [];
            if (reservation.contactDetails) {
              if (reservation.contactDetails.email) {
                contacts.push({
                  type: 'email',
                  value: reservation.contactDetails.email,
                  isPrimary: true
                });
              }
              if (reservation.contactDetails.phone) {
                contacts.push({
                  type: 'phone',
                  value: reservation.contactDetails.phone,
                  isPrimary: true
                });
              }
            }

            // Eğer hiç contact yoksa default değerler ekle
            if (contacts.length === 0) {
              contacts.push(
                {
                  type: 'email',
                  value: '',
                  isPrimary: true
                },
                {
                  type: 'phone',
                  value: '',
                  isPrimary: true
                }
              );
            }

            return contacts;
          })()
        });
      } else {
        toast.error('Rezervasyon bulunamadı');
        navigate('/reservations');
      }
    } catch (error) {
      console.error('Error loading reservation data:', error);
      toast.error('Rezervasyon verileri yüklenirken bir hata oluştu');
      navigate('/reservations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePassengerChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      passengers: prev.passengers.map((passenger, i) => 
        i === index ? { ...passenger, [field]: value } : passenger
      )
    }));
  };

  const handleTransferChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      transfers: prev.transfers.map((transfer, i) => 
        i === index ? { ...transfer, [field]: value } : transfer
      )
    }));
  };

  const handleContactChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      contactDetails: prev.contactDetails.map((contact, i) => 
        i === index ? { ...contact, [field]: value } : contact
      )
    }));
  };

  const addPassenger = () => {
    setFormData(prev => ({
      ...prev,
      passengers: [...prev.passengers, {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        nationality: '',
        identityNumber: '',
        birthDate: '',
        gender: '',
        specialRequests: ''
      }],
      passengerCount: prev.passengerCount + 1
    }));
  };

  const removePassenger = (index) => {
    if (formData.passengers.length > 1) {
      setFormData(prev => ({
        ...prev,
        passengers: prev.passengers.filter((_, i) => i !== index),
        passengerCount: prev.passengerCount - 1
      }));
    }
  };

  const addTransfer = () => {
    setFormData(prev => ({
      ...prev,
      transfers: [...prev.transfers, {
        direction: 'departure',
        date: '',
        time: '',
        flightNumber: '',
        pickupLocation: '',
        dropoffLocation: '',
        notes: ''
      }]
    }));
  };

  const removeTransfer = (index) => {
    if (formData.transfers.length > 1) {
      setFormData(prev => ({
        ...prev,
        transfers: prev.transfers.filter((_, i) => i !== index)
      }));
    }
  };

  const addContact = () => {
    setFormData(prev => ({
      ...prev,
      contactDetails: [...prev.contactDetails, {
        type: 'email',
        value: '',
        isPrimary: false
      }]
    }));
  };

  const removeContact = (index) => {
    if (formData.contactDetails.length > 1) {
      setFormData(prev => ({
        ...prev,
        contactDetails: prev.contactDetails.filter((_, i) => i !== index)
      }));
    }
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return formData.agencyId && formData.fromRegionId && formData.toRegionId && formData.vehicleTypeId;
      case 2:
        return formData.passengers.every(p => p.firstName && p.lastName);
      case 3:
        return formData.transfers.every(t => t.date && t.time);
      case 4:
        return formData.contactDetails.some(c => c.value);
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    } else {
      toast.error('Lütfen gerekli alanları doldurun');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      toast.error('Lütfen tüm gerekli alanları doldurun');
      return;
    }

    setIsSubmitting(true);
    try {
      // Backend'in beklediği formata dönüştür
      const formattedData = {
        agencyId: formData.agencyId,
        agencyReference: formData.agencyReference,
        fromRegionId: formData.fromRegionId,
        toRegionId: formData.toRegionId,
        hotelId: null,
        hotelName: formData.hotelName,
        secondaryHotelId: null,
        secondaryHotelName: formData.secondaryHotelName,
        vehicleTypeId: formData.vehicleTypeId,
        isRoundTrip: formData.isRoundTrip,
        passengerCount: formData.passengerCount,
        price: formData.price,
        currency: formData.currency,

        // Yolcu bilgileri
        passengers: formData.passengers.map(passenger => ({
          firstName: passenger.firstName,
          lastName: passenger.lastName,
          email: passenger.email,
          phone: passenger.phone,
          nationality: passenger.nationality,
          identityNumber: passenger.identityNumber,
          birthDate: passenger.birthDate,
          gender: passenger.gender,
          specialRequests: passenger.specialRequests
        })),

        // Transfer bilgileri
        transfers: formData.transfers.map(transfer => ({
          isReturn: transfer.direction === 'departure',
          transferDate: transfer.date,
          transferTime: transfer.time,
          flightNumber: transfer.flightNumber,
          pickupLocation: transfer.pickupLocation,
          dropoffLocation: transfer.dropoffLocation,
          notes: transfer.notes
        })),

        // İletişim bilgileri
        contactDetails: formData.contactDetails.map(contact => ({
          type: contact.type,
          value: contact.value,
          isPrimary: contact.isPrimary
        }))
      };

      const response = await reservationService.updateReservation(id, formattedData);

      if (response && response.success) {
        toast.success('Rezervasyon başarıyla güncellendi');
        navigate(`/reservations/${id}`);
      } else {
        toast.error(response?.message || 'Rezervasyon güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating reservation:', error);
      toast.error(error.response?.data?.message || 'Rezervasyon güncellenirken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(`/reservations/${id}`)}
          className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Rezervasyon Detayına Dön
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Rezervasyon Düzenle</h1>
        <p className="text-gray-600">Rezervasyon bilgilerini güncelleyin</p>
      </div>

      {/* Progress Bar */}
      <StepProgressBar steps={steps} currentStep={currentStep} />

      {/* Form Content */}
      <div className="mt-8">
        {currentStep === 1 && (
          <ReservationBasicInfo
            formData={formData}
            agencies={agencies}
            regions={regions}
            vehicleTypes={vehicleTypes}
            onChange={handleInputChange}
          />
        )}

        {currentStep === 2 && (
          <PassengerInfo
            passengers={formData.passengers}
            onChange={handlePassengerChange}
            onAdd={addPassenger}
            onRemove={removePassenger}
          />
        )}

        {currentStep === 3 && (
          <TransferInfo
            transfers={formData.transfers}
            onChange={handleTransferChange}
            onAdd={addTransfer}
            onRemove={removeTransfer}
          />
        )}

        {currentStep === 4 && (
          <ContactInfo
            contactDetails={formData.contactDetails}
            onChange={handleContactChange}
            onAdd={addContact}
            onRemove={removeContact}
          />
        )}

        {currentStep === 5 && (
          <PriceSummary
            formData={formData}
            agencies={agencies}
            regions={regions}
            vehicleTypes={vehicleTypes}
          />
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="mt-8 flex justify-between">
        <button
          type="button"
          onClick={prevStep}
          disabled={currentStep === 1}
          className={`px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium ${
            currentStep === 1
              ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
              : 'text-gray-700 bg-white hover:bg-gray-50'
          }`}
        >
          Önceki
        </button>

        <div className="flex space-x-3">
          {currentStep < steps.length ? (
            <button
              type="button"
              onClick={nextStep}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Sonraki
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {isSubmitting ? 'Güncelleniyor...' : 'Rezervasyonu Güncelle'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReservationEdit;
