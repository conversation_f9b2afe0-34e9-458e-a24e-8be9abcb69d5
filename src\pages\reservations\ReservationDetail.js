import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { reservationService } from '../../services/reservationService';

const ReservationDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [reservation, setReservation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchReservationDetail();
  }, [id]);

  const fetchReservationDetail = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await reservationService.getReservationById(id);
      
      if (response.success) {
        setReservation(response.reservation);
      } else {
        setError('Rezervasyon bulunamadı');
      }
    } catch (error) {
      console.error('Rezervasyon detayı yüklenirken hata:', error);
      setError('Rezervasyon detayı yüklenirken bir hata oluştu');
      toast.error('Rezervasyon detayı yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'Onaylandı';
      case 'pending':
        return 'Beklemede';
      case 'cancelled':
        return 'İptal Edildi';
      case 'completed':
        return 'Tamamlandı';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">{error}</div>
          <button
            onClick={() => navigate('/reservations')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Rezervasyonlara Dön
          </button>
        </div>
      </div>
    );
  }

  if (!reservation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600 text-xl mb-4">Rezervasyon bulunamadı</div>
          <button
            onClick={() => navigate('/reservations')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Rezervasyonlara Dön
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <button
              onClick={() => navigate('/reservations')}
              className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Rezervasyonlara Dön
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              Rezervasyon Detayı
            </h1>
            <p className="text-gray-600">
              {reservation.reservation_number}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(reservation.status)}`}>
              {getStatusText(reservation.status)}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Ana Bilgiler */}
        <div className="lg:col-span-2 space-y-6">
          {/* Temel Bilgiler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Temel Bilgiler</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Rezervasyon Numarası</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.reservation_number}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Acente Referansı</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.agency_reference || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Acente</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.agency?.name || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Oluşturma Tarihi</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(reservation.created_at)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Nereden</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.fromRegion?.name || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Nereye</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.toRegion?.name || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Araç Tipi</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.vehicleType?.name || '-'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Yolcu Sayısı</label>
                <p className="mt-1 text-sm text-gray-900">{reservation.passenger_count || '-'}</p>
              </div>
            </div>
          </div>

          {/* Yolcu Bilgileri */}
          {reservation.passengers && reservation.passengers.length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Yolcu Bilgileri</h2>
              <div className="space-y-4">
                {reservation.passengers.map((passenger, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Yolcu {index + 1}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Ad Soyad</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {passenger.first_name} {passenger.last_name}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">E-posta</label>
                        <p className="mt-1 text-sm text-gray-900">{passenger.email || '-'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Telefon</label>
                        <p className="mt-1 text-sm text-gray-900">{passenger.phone || '-'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Uyruk</label>
                        <p className="mt-1 text-sm text-gray-900">{passenger.nationality || '-'}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Transfer Bilgileri */}
          {reservation.transfers && reservation.transfers.length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Transfer Bilgileri</h2>
              <div className="space-y-4">
                {reservation.transfers.map((transfer, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-gray-900">
                        {transfer.is_return ? 'Dönüş Transferi' : 'Varış Transferi'}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transfer.status)}`}>
                        {getStatusText(transfer.status)}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Tarih</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {transfer.transfer_date ? new Date(transfer.transfer_date).toLocaleDateString('tr-TR') : '-'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Saat</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {transfer.transfer_time ? transfer.transfer_time.substring(0, 5) : '-'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Uçuş Numarası</label>
                        <p className="mt-1 text-sm text-gray-900">{transfer.flight_number || '-'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">Oda Numarası</label>
                        <p className="mt-1 text-sm text-gray-900">{transfer.room_number || '-'}</p>
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-500">Nakit Ödeme</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {transfer.cash_payment} {transfer.cash_currency}
                        </p>
                      </div>
                      {(transfer.driver_note || transfer.operation_note) && (
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-500">Notlar</label>
                          <p className="mt-1 text-sm text-gray-900">
                            {transfer.driver_note && (
                              <span><strong>Şoför Notu:</strong> {transfer.driver_note}<br /></span>
                            )}
                            {transfer.operation_note && (
                              <span><strong>Operasyon Notu:</strong> {transfer.operation_note}</span>
                            )}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Yan Panel */}
        <div className="space-y-6">
          {/* Fiyat Bilgileri */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Fiyat Bilgileri</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Toplam Fiyat</span>
                <span className="text-lg font-medium text-gray-900">
                  {reservation.price} {reservation.currency}
                </span>
              </div>
            </div>
          </div>

          {/* İletişim Bilgileri */}
          {reservation.contactDetails && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">İletişim Bilgileri</h2>
              <div className="space-y-3">
                {reservation.contactDetails.email && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">E-posta</label>
                    <p className="mt-1 text-sm text-gray-900">{reservation.contactDetails.email}</p>
                  </div>
                )}
                {reservation.contactDetails.phone && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Telefon</label>
                    <p className="mt-1 text-sm text-gray-900">{reservation.contactDetails.phone}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* İşlemler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">İşlemler</h2>
            <div className="space-y-3">
              <button
                onClick={() => navigate(`/reservations/${id}/edit`)}
                className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
              >
                Düzenle
              </button>
              <button
                onClick={() => reservationService.generateVoucher(id)}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                Voucher Oluştur
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReservationDetail;
