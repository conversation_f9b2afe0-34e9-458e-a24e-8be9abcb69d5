import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';

// Layouts
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';

// Auth Pages
import Login from './pages/auth/Login';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';

// Dashboard Pages
import Dashboard from './pages/dashboard/Dashboard';

// Region Pages
import RegionList from './pages/regions/RegionList';
import RegionCreate from './pages/regions/RegionCreate';
import RegionEdit from './pages/regions/RegionEdit';

// Agency Pages
import AgencyList from './pages/agencies/AgencyList';
import AgencyCreate from './pages/agencies/AgencyCreate';
import AgencyEdit from './pages/agencies/AgencyEdit';
import AgencyDetail from './pages/agencies/AgencyDetail';

// Carrier Pages
import CarrierList from './pages/carriers/CarrierList';
import CarrierCreate from './pages/carriers/CarrierCreate';
import CarrierEdit from './pages/carriers/CarrierEdit';
import CarrierDetail from './pages/carriers/CarrierDetail';

// Vehicle Pages
import VehicleList from './pages/vehicles/VehicleList';
import VehicleCreate from './pages/vehicles/VehicleCreate';
import VehicleEdit from './pages/vehicles/VehicleEdit';
import VehicleDetail from './pages/vehicles/VehicleDetail';

// Price Pages
import PriceList from './pages/prices/PriceList';
import PriceCreate from './pages/prices/PriceCreate';
import PriceEdit from './pages/prices/PriceEdit';
import PriceBulkUpdate from './pages/prices/PriceBulkUpdate';

// Reservation Pages
import ReservationList from './pages/reservations/ReservationList';
import ReservationCreate from './pages/reservations/ReservationCreate';
import ReservationDetail from './pages/reservations/ReservationDetail';

// Operation Pages
import OperationList from './pages/operations/OperationList';

// User Pages
import UserList from './pages/users/UserList';
import UserCreate from './pages/users/UserCreate';
import UserEdit from './pages/users/UserEdit';
import UserPermissions from './pages/users/UserPermissions';
import ChangePassword from './pages/users/ChangePassword';

// Not Found Page
import NotFound from './pages/NotFound';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
    </div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

// Public Route Component (redirects if already authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
    </div>;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

function App() {
  return (
    <Routes>
      {/* Auth Routes */}
      <Route path="/" element={<PublicRoute><AuthLayout /></PublicRoute>}>
        <Route index element={<Navigate to="/login" replace />} />
        <Route path="login" element={<Login />} />
        <Route path="forgot-password" element={<ForgotPassword />} />
        <Route path="reset-password" element={<ResetPassword />} />
      </Route>

      {/* Protected Routes */}
      <Route path="/" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>
        <Route path="dashboard" element={<Dashboard />} />

        {/* Region Routes */}
        <Route path="regions" element={<RegionList />} />
        <Route path="regions/create" element={<RegionCreate />} />
        <Route path="regions/:id/edit" element={<RegionEdit />} />

        {/* Agency Routes */}
        <Route path="agencies" element={<AgencyList />} />
        <Route path="agencies/create" element={<AgencyCreate />} />
        <Route path="agencies/:id/edit" element={<AgencyEdit />} />
        <Route path="agencies/:id" element={<AgencyDetail />} />

        {/* Carrier Routes */}
        <Route path="carriers" element={<CarrierList />} />
        <Route path="carriers/create" element={<CarrierCreate />} />
        <Route path="carriers/:id/edit" element={<CarrierEdit />} />
        <Route path="carriers/:id" element={<CarrierDetail />} />

        {/* Vehicle Routes */}
        <Route path="vehicles" element={<VehicleList />} />
        <Route path="vehicles/create" element={<VehicleCreate />} />
        <Route path="vehicles/:id/edit" element={<VehicleEdit />} />
        <Route path="vehicles/:id" element={<VehicleDetail />} />

        {/* Price Routes */}
        <Route path="prices" element={<PriceList />} />
        <Route path="prices/create" element={<PriceCreate />} />
        <Route path="prices/bulk" element={<PriceBulkUpdate />} />
        <Route path="prices/:id/edit" element={<PriceEdit />} />

        {/* Reservation Routes */}
        <Route path="reservations" element={<ReservationList />} />
        <Route path="reservations/create" element={<ReservationCreate />} />
        <Route path="reservations/:id" element={<ReservationDetail />} />

        {/* Operation Routes */}
        <Route path="operations" element={<OperationList />} />

        {/* User Routes */}
        <Route path="users" element={<UserList />} />
        <Route path="users/create" element={<UserCreate />} />
        <Route path="users/:id/edit" element={<UserEdit />} />
        <Route path="users/:id/permissions" element={<UserPermissions />} />
        <Route path="users/:id/change-password" element={<ChangePassword />} />
      </Route>

      {/* Not Found Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
