import React from 'react';

const StepProgressBar = ({ currentStep, steps }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id || index} className="relative flex flex-col items-center">
            {/* Bağlantı çizgisi */}
            {index < steps.length - 1 && (
              <div className="absolute top-4 w-full h-0.5 left-1/2" aria-hidden="true">
                <div 
                  className={`h-0.5 ${
                    index < currentStep - 1 
                      ? 'bg-primary-600' 
                      : 'bg-gray-200'
                  }`} 
                  style={{ width: 'calc(100% - 2rem)' }}
                />
              </div>
            )}
            
            {/* Adım dairesi */}
            <div
              className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                index + 1 < currentStep
                  ? 'bg-primary-600 border-primary-600'
                  : index + 1 === currentStep
                  ? 'border-primary-600 bg-white'
                  : 'bg-white border-gray-300'
              }`}
            >
              {index + 1 < currentStep ? (
                <svg className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path
                    fillRule="evenodd"
                    d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <span
                  className={`text-sm font-medium ${
                    index + 1 === currentStep ? 'text-primary-600' : 'text-gray-500'
                  }`}
                >
                  {index + 1}
                </span>
              )}
            </div>
            
            {/* Adım metni */}
            <div className="mt-2 text-center">
              <span 
                className={`text-sm font-medium ${
                  index + 1 === currentStep 
                    ? 'text-primary-600' 
                    : index + 1 < currentStep 
                      ? 'text-primary-500' 
                      : 'text-gray-500'
                }`}
              >
                {step.name || step}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepProgressBar;
