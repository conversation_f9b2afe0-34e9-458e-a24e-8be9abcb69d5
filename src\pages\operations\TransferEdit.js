import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { transferService } from '../../services/transferService';
import { carrierService } from '../../services/carrierService';
import { vehicleService } from '../../services/vehicleService';

const TransferEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transfer, setTransfer] = useState(null);
  const [carriers, setCarriers] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const [formData, setFormData] = useState({
    transfer_date: '',
    transfer_time: '',
    flight_number: '',
    room_number: '',
    baby_seat_count: 0,
    child_seat_count: 0,
    booster_count: 0,
    cash_payment: '',
    cash_currency: 'EUR',
    driver_note: '',
    operation_note: '',
    status: 'pending',
    carrier_id: '',
    vehicle_id: ''
  });

  useEffect(() => {
    loadInitialData();
  }, [id]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      
      // Paralel olarak verileri yükle
      const [transferResponse, carriersResponse, vehiclesResponse] = await Promise.all([
        transferService.getTransferById(id),
        carrierService.getCarriers({ isActive: true }),
        vehicleService.getVehicles({ isActive: true })
      ]);

      // Transfer verilerini yükle
      if (transferResponse?.success && transferResponse.transfer) {
        const transferData = transferResponse.transfer;
        setTransfer(transferData);
        
        setFormData({
          transfer_date: transferData.transfer_date || '',
          transfer_time: transferData.transfer_time ? transferData.transfer_time.substring(0, 5) : '',
          flight_number: transferData.flight_number || '',
          room_number: transferData.room_number || '',
          baby_seat_count: transferData.baby_seat_count || 0,
          child_seat_count: transferData.child_seat_count || 0,
          booster_count: transferData.booster_count || 0,
          cash_payment: transferData.cash_payment || '',
          cash_currency: transferData.cash_currency || 'EUR',
          driver_note: transferData.driver_note || '',
          operation_note: transferData.operation_note || '',
          status: transferData.status || 'pending',
          carrier_id: transferData.carrier_id || '',
          vehicle_id: transferData.vehicle_id || ''
        });
      } else {
        toast.error('Transfer bulunamadı');
        navigate('/operations/transfers');
        return;
      }

      // Dropdown verilerini set et
      if (carriersResponse?.success) {
        setCarriers(carriersResponse.data?.rows || []);
      }
      if (vehiclesResponse?.success) {
        setVehicles(vehiclesResponse.data?.vehicles || []);
      }
    } catch (error) {
      console.error('Error loading transfer data:', error);
      toast.error('Transfer verileri yüklenirken bir hata oluştu');
      navigate('/operations/transfers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.transfer_date || !formData.transfer_time) {
      toast.error('Transfer tarihi ve saati zorunludur');
      return;
    }

    setIsSaving(true);
    try {
      const response = await transferService.updateTransfer(id, formData);
      
      if (response?.success) {
        toast.success('Transfer başarıyla güncellendi');
        navigate(`/operations/transfers/${id}`);
      } else {
        toast.error(response?.message || 'Transfer güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating transfer:', error);
      toast.error(error.response?.data?.message || 'Transfer güncellenirken bir hata oluştu');
    } finally {
      setIsSaving(false);
    }
  };

  const getStatusOptions = () => [
    { value: 'pending', label: 'Beklemede' },
    { value: 'assigned', label: 'Atandı' },
    { value: 'in_progress', label: 'Devam Ediyor' },
    { value: 'completed', label: 'Tamamlandı' },
    { value: 'cancelled', label: 'İptal Edildi' }
  ];

  const getCurrencyOptions = () => [
    { value: 'EUR', label: 'EUR' },
    { value: 'USD', label: 'USD' },
    { value: 'TRY', label: 'TRY' }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!transfer) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600 text-xl mb-4">Transfer bulunamadı</div>
          <button
            onClick={() => navigate('/operations/transfers')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Transferlere Dön
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate(`/operations/transfers/${id}`)}
          className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Transfer Detayına Dön
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Transfer Düzenle</h1>
        <p className="text-gray-600">
          Rezervasyon: {transfer.reservation?.reservation_number}
        </p>
      </div>

      {/* Form */}
      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Temel Bilgiler */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Temel Bilgiler</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Transfer Tarihi</label>
                <input
                  type="date"
                  name="transfer_date"
                  value={formData.transfer_date}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Transfer Saati</label>
                <input
                  type="time"
                  name="transfer_time"
                  value={formData.transfer_time}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Uçuş Numarası</label>
                <input
                  type="text"
                  name="flight_number"
                  value={formData.flight_number}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Oda Numarası</label>
                <input
                  type="text"
                  name="room_number"
                  value={formData.room_number}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>

          {/* Çocuk Koltuğu Bilgileri */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Çocuk Koltuğu Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Bebek Koltuğu</label>
                <input
                  type="number"
                  name="baby_seat_count"
                  value={formData.baby_seat_count}
                  onChange={handleInputChange}
                  min="0"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Çocuk Koltuğu</label>
                <input
                  type="number"
                  name="child_seat_count"
                  value={formData.child_seat_count}
                  onChange={handleInputChange}
                  min="0"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Yükseltici Koltuk</label>
                <input
                  type="number"
                  name="booster_count"
                  value={formData.booster_count}
                  onChange={handleInputChange}
                  min="0"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>

          {/* Nakit Ödeme */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Nakit Ödeme</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Nakit Ödeme Tutarı</label>
                <input
                  type="number"
                  name="cash_payment"
                  value={formData.cash_payment}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Para Birimi</label>
                <select
                  name="cash_currency"
                  value={formData.cash_currency}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                >
                  {getCurrencyOptions().map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Atama Bilgileri */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Atama Bilgileri</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Taşıyıcı</label>
                <select
                  name="carrier_id"
                  value={formData.carrier_id}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Taşıyıcı Seçin</option>
                  {carriers.map(carrier => (
                    <option key={carrier.id} value={carrier.id}>
                      {carrier.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Araç</label>
                <select
                  name="vehicle_id"
                  value={formData.vehicle_id}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="">Araç Seçin</option>
                  {vehicles.map(vehicle => (
                    <option key={vehicle.id} value={vehicle.id}>
                      {vehicle.plate_number} - {vehicle.vehicleType?.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Durum */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Durum</h2>
            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Transfer Durumu</label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                >
                  {getStatusOptions().map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Notlar */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Notlar</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Şoför Notu</label>
                <textarea
                  name="driver_note"
                  value={formData.driver_note}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Operasyon Notu</label>
                <textarea
                  name="operation_note"
                  value={formData.operation_note}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={() => navigate(`/operations/transfers/${id}`)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isSaving}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TransferEdit;
