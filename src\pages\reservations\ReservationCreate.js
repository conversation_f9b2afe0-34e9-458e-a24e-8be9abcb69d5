import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { reservationService } from '../../services/reservationService';
import { agencyService } from '../../services/agencyService';
import { regionService } from '../../services/regionService';
import { vehicleService } from '../../services/vehicleService';
import ReservationBasicInfo from './components/ReservationBasicInfo';
import PassengerInfo from './components/PassengerInfo';
import TransferInfo from './components/TransferInfo';
import ContactInfo from './components/ContactInfo';
import PriceSummary from './components/PriceSummary';
import StepProgressBar from './components/StepProgressBar';

const ReservationCreate = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agencies, setAgencies] = useState([]);
  const [regions, setRegions] = useState([]);
  const [vehicleTypes, setVehicleTypes] = useState([]);

  // Form verileri
  const [formData, setFormData] = useState({
    // Temel bilgiler
    agencyId: '',
    agencyReference: '',
    fromRegionId: '',
    toRegionId: '',
    hotelName: '',
    secondaryHotelName: '',
    isRoundTrip: false,
    vehicleTypeId: '',
    price: '',
    currency: 'EUR',
    passengerCount: 1,

    // Yolcu bilgileri
    passengers: [
      {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        nationality: '',
        identityNumber: '',
        birthDate: '',
        gender: '',
        specialRequests: ''
      }
    ],

    // Transfer bilgileri
    transfers: [
      {
        direction: 'arrival', // arrival veya departure
        date: '',
        time: '',
        flightNumber: '',
        pickupLocation: '',
        dropoffLocation: '',
        notes: ''
      }
    ],

    // İletişim bilgileri
    contactDetails: [
      {
        type: 'email',
        value: '',
        isPrimary: true
      },
      {
        type: 'phone',
        value: '',
        isPrimary: true
      }
    ]
  });

  useEffect(() => {
    fetchAgencies();
    fetchRegions();
    fetchVehicleTypes();
  }, []);

  const fetchAgencies = async () => {
    try {
      const response = await agencyService.getAgencies({ isActive: true });
      if (response && response.success && response.data) {
        setAgencies(response.data.agencies);
      }
    } catch (error) {
      console.error('Error fetching agencies:', error);
      toast.error('Acenteler yüklenirken bir hata oluştu');
    }
  };

  const fetchRegions = async () => {
    try {
      const response = await regionService.getRegions();
      if (response && response.success && response.data) {
        setRegions(response.data.regions);
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      toast.error('Bölgeler yüklenirken bir hata oluştu');
    }
  };

  const fetchVehicleTypes = async () => {
    try {
      const response = await vehicleService.getVehicleTypes();
      if (response && response.success && response.data) {
        setVehicleTypes(response.data.vehicleTypes);
      }
    } catch (error) {
      console.error('Error fetching vehicle types:', error);
      toast.error('Araç tipleri yüklenirken bir hata oluştu');
    }
  };

  const handleBasicInfoChange = (data) => {
    setFormData(prev => ({
      ...prev,
      ...data
    }));

    // Yolcu sayısı değiştiyse, yolcu listesini güncelle
    if (data.passengerCount !== undefined && data.passengerCount !== formData.passengerCount) {
      const newPassengerCount = parseInt(data.passengerCount);
      const currentPassengers = [...formData.passengers];

      if (newPassengerCount > currentPassengers.length) {
        // Yeni yolcular ekle
        const newPassengers = [...currentPassengers];
        for (let i = currentPassengers.length; i < newPassengerCount; i++) {
          newPassengers.push({
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            nationality: '',
            identityNumber: '',
            birthDate: '',
            gender: '',
            specialRequests: ''
          });
        }
        setFormData(prev => ({
          ...prev,
          passengers: newPassengers
        }));
      } else if (newPassengerCount < currentPassengers.length) {
        // Fazla yolcuları kaldır
        setFormData(prev => ({
          ...prev,
          passengers: currentPassengers.slice(0, newPassengerCount)
        }));
      }
    }

    // Gidiş-dönüş seçeneği değiştiyse, transfer listesini güncelle
    if (data.isRoundTrip !== undefined && data.isRoundTrip !== formData.isRoundTrip) {
      if (data.isRoundTrip) {
        // Dönüş transferi ekle
        if (!formData.transfers.some(t => t.direction === 'departure')) {
          setFormData(prev => ({
            ...prev,
            transfers: [
              ...prev.transfers,
              {
                direction: 'departure',
                date: '',
                time: '',
                flightNumber: '',
                pickupLocation: '',
                dropoffLocation: '',
                notes: ''
              }
            ]
          }));
        }
      } else {
        // Dönüş transferini kaldır
        setFormData(prev => ({
          ...prev,
          transfers: prev.transfers.filter(t => t.direction === 'arrival')
        }));
      }
    }
  };

  const handlePassengerInfoChange = (passengers) => {
    setFormData(prev => ({
      ...prev,
      passengers
    }));
  };

  const handleTransferInfoChange = (transfers) => {
    setFormData(prev => ({
      ...prev,
      transfers
    }));
  };

  const handleContactInfoChange = (contactDetails) => {
    setFormData(prev => ({
      ...prev,
      contactDetails
    }));
  };

  const nextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, 5));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const validateStep = (step) => {
    switch (step) {
      case 1: // Temel bilgiler
        if (!formData.agencyId) return 'Acente seçimi zorunludur';
        if (!formData.fromRegionId) return 'Başlangıç bölgesi seçimi zorunludur';
        if (!formData.toRegionId) return 'Varış bölgesi seçimi zorunludur';
        if (!formData.vehicleTypeId) return 'Araç tipi seçimi zorunludur';
        if (!formData.price) return 'Fiyat zorunludur';
        if (formData.passengerCount < 1) return 'En az 1 yolcu olmalıdır';
        return null;

      case 2: // Yolcu bilgileri
        for (let i = 0; i < formData.passengers.length; i++) {
          const passenger = formData.passengers[i];
          if (!passenger.firstName) return `Yolcu ${i+1}: Ad zorunludur`;
          if (!passenger.lastName) return `Yolcu ${i+1}: Soyad zorunludur`;
        }
        return null;

      case 3: // Transfer bilgileri
        for (let i = 0; i < formData.transfers.length; i++) {
          const transfer = formData.transfers[i];
          if (!transfer.date) {
            return `Transfer ${i+1}: Tarih zorunludur`;
          }
          if (!transfer.time) {
            return `Transfer ${i+1}: Saat zorunludur`;
          }
          // Tarih formatını kontrol et (YYYY-MM-DD)
          if (!/^\d{4}-\d{2}-\d{2}$/.test(transfer.date)) {
            return `Transfer ${i+1}: Tarih formatı geçersiz. Lütfen YYYY-MM-DD formatında giriniz.`;
          }
          // Saat formatını kontrol et (HH:MM)
          if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(transfer.time)) {
            return `Transfer ${i+1}: Saat formatı geçersiz. Lütfen HH:MM formatında giriniz.`;
          }
        }
        return null;

      case 4: // İletişim bilgileri
       
        const phoneContact = formData.contactDetails.find(c => c.type === 'phone' && c.isPrimary);


        if (!phoneContact || !phoneContact.value) return 'Telefon numarası zorunludur';
        return null;

      default:
        return null;
    }
  };

  const handleStepChange = (direction) => {
    if (direction === 'next') {
      const error = validateStep(currentStep);
      if (error) {
        toast.error(error);
        return;
      }
      nextStep();
    } else {
      prevStep();
    }
  };

  const handleSubmit = async () => {
    // Son adımı doğrula
    const error = validateStep(currentStep);
    if (error) {
      toast.error(error);
      return;
    }

    setIsSubmitting(true);
    try {
      // Backend'in beklediği formata dönüştür
      const formattedData = {
        agencyId: formData.agencyId,
        agencyReference: formData.agencyReference,
        fromRegionId: formData.fromRegionId,
        toRegionId: formData.toRegionId,
        hotelId: null, // Otel ID yerine otel adı kullanıyoruz
        hotelName: formData.hotelName, // Otel adını ekleyelim
        secondaryHotelId: null, // İkinci otel ID yerine otel adı kullanıyoruz
        secondaryHotelName: formData.secondaryHotelName, // İkinci otel adını ekleyelim
        vehicleTypeId: formData.vehicleTypeId,
        isRoundTrip: formData.isRoundTrip,
        passengerCount: formData.passengerCount,
        price: formData.price,
        currency: formData.currency,
        status: 'pending',

        // Yolcu bilgileri
        passengers: formData.passengers.map(passenger => ({
          firstName: passenger.firstName,
          lastName: passenger.lastName,
          email: passenger.email,
          phone: passenger.phone,
          nationality: passenger.nationality,
          identityNumber: passenger.identityNumber,
          birthDate: passenger.birthDate,
          gender: passenger.gender,
          specialRequests: passenger.specialRequests
        })),

        // Transfer bilgileri
        transfers: formData.transfers.map(transfer => ({
          isReturn: transfer.direction === 'departure',
          transferDate: transfer.date,
          transferTime: transfer.time,
          flightNumber: transfer.flightNumber,
          pickupLocation: transfer.pickupLocation,
          dropoffLocation: transfer.dropoffLocation,
          notes: transfer.notes
        })),

        // İletişim bilgileri
        contactDetails: formData.contactDetails.map(contact => ({
          type: contact.type,
          value: contact.value,
          isPrimary: contact.isPrimary
        }))
      };

      const response = await reservationService.createReservation(formattedData);

      if (response && response.success) {
        toast.success('Rezervasyon başarıyla oluşturuldu');
        navigate(`/reservations/${response.reservation.id}`);
      } else {
        toast.error(response?.message || 'Rezervasyon oluşturulurken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error creating reservation:', error);
      toast.error(error.response?.data?.message || 'Rezervasyon oluşturulurken bir hata oluştu');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ReservationBasicInfo
            formData={formData}
            onChange={handleBasicInfoChange}
            agencies={agencies}
            regions={regions}
            vehicleTypes={vehicleTypes}
          />
        );
      case 2:
        return (
          <PassengerInfo
            passengers={formData.passengers}
            onChange={handlePassengerInfoChange}
          />
        );
      case 3:
        return (
          <TransferInfo
            transfers={formData.transfers}
            isRoundTrip={formData.isRoundTrip}
            onChange={handleTransferInfoChange}
          />
        );
      case 4:
        return (
          <ContactInfo
            contactDetails={formData.contactDetails}
            onChange={handleContactInfoChange}
          />
        );
      case 5:
        return (
          <PriceSummary
            formData={formData}
            agencies={agencies}
            regions={regions}
            vehicleTypes={vehicleTypes}
          />
        );
      default:
        return null;
    }
  };

  const steps = ['Temel Bilgiler', 'Yolcu Bilgileri', 'Transfer Bilgileri', 'İletişim Bilgileri', 'Özet'];

  return (
    <div>
      <div className="pb-5 border-b border-gray-200">
        <h1 className="text-2xl font-semibold text-gray-900">Yeni Rezervasyon</h1>
      </div>

      <div className="mt-6 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
        {/* Adım göstergesi */}
        <StepProgressBar currentStep={currentStep} steps={steps} />

        {/* Adım içeriği */}
        <div className="mt-8">
          {renderStepContent()}
        </div>

        {/* Navigasyon butonları */}
        <div className="mt-8 flex justify-between">
          <button
            type="button"
            onClick={() => handleStepChange('prev')}
            disabled={currentStep === 1}
            className={`inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
              currentStep === 1 ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            Geri
          </button>

          {currentStep < 5 ? (
            <button
              type="button"
              onClick={() => handleStepChange('next')}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              İleri
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isSubmitting ? 'Kaydediliyor...' : 'Rezervasyonu Oluştur'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReservationCreate;
