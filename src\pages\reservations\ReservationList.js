import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { reservationService } from '../../services/reservationService';
import { toast } from 'react-toastify';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  DocumentTextIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const ReservationList = () => {
  const [reservations, setReservations] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 10
  });
  const [isLoading, setIsLoading] = useState(true);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedReservation, setSelectedReservation] = useState(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    agencyId: '',
    fromDate: '',
    toDate: '',
    searchTerm: '',
    page: 1,
    limit: 10
  });

  useEffect(() => {
    fetchReservations();
  }, [filters]);

  const fetchReservations = async () => {
    setIsLoading(true);
    try {
      const response = await reservationService.getReservations(filters);
      
      if (response && response.success && response.data) {
        setReservations(response.data.reservations || []);
        setPagination(response.data.pagination || {
          currentPage: parseInt(filters.page),
          totalPages: Math.ceil((response.data.total || 0) / filters.limit),
          total: response.data.total || 0,
          limit: parseInt(filters.limit)
        });
      } else {
        setReservations([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          total: 0,
          limit: parseInt(filters.limit)
        });
      }
    } catch (error) {
      toast.error('Rezervasyonlar yüklenirken bir hata oluştu');
      console.error('Error fetching reservations:', error);
      setReservations([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value,
      page: 1 // Filtre değiştiğinde ilk sayfaya dön
    }));
  };

  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      setFilters(prev => ({
        ...prev,
        page: newPage
      }));
    }
  };

  const handleCancelReservation = async (id) => {
    if (window.confirm('Bu rezervasyonu iptal etmek istediğinize emin misiniz?')) {
      try {
        const reason = prompt('İptal sebebini giriniz:');
        if (reason === null) return; // İptal edildi
        
        await reservationService.cancelReservation(id, { reason });
        toast.success('Rezervasyon başarıyla iptal edildi');
        fetchReservations();
      } catch (error) {
        toast.error('Rezervasyon iptal edilirken bir hata oluştu');
        console.error('Error cancelling reservation:', error);
      }
    }
  };

  const handleStatusChange = async (newStatus, notes = '') => {
    if (!selectedReservation) return;

    setStatusUpdateLoading(true);
    try {
      await reservationService.updateReservationStatus(selectedReservation.id, {
        status: newStatus,
        notes: notes
      });

      // Local state'i güncelle
      setReservations(prev => prev.map(reservation =>
        reservation.id === selectedReservation.id
          ? { ...reservation, status: newStatus }
          : reservation
      ));

      toast.success('Rezervasyon durumu başarıyla güncellendi');
      setShowStatusModal(false);
      setSelectedReservation(null);
    } catch (error) {
      toast.error('Rezervasyon durumu güncellenirken bir hata oluştu');
      console.error('Error updating reservation status:', error);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'no_show':
        return 'bg-gray-100 text-gray-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'Onaylandı';
      case 'pending':
        return 'Beklemede';
      case 'in_progress':
        return 'Devam ediyor';
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal edildi';
      case 'no_show':
        return 'Gelmedi';
      case 'failed':
        return 'Başarısız';
      default:
        return status;
    }
  };

  const formatCurrency = (amount, currency) => {
    return new Intl.NumberFormat('tr-TR', { style: 'currency', currency: currency || 'EUR' }).format(amount);
  };

  return (
    <div className="w-full max-w-none px-4 sm:px-6 lg:px-8">
      <div className="pb-5 border-b border-gray-200 sm:flex sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Rezervasyonlar</h1>
        <div className="mt-3 sm:mt-0 sm:ml-4">
          <Link
            to="/reservations/create"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            Yeni Rezervasyon
          </Link>
        </div>
      </div>

      {/* Filtreler */}
      <div className="mt-4 bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
              Durum
            </label>
            <select
              id="status"
              name="status"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <option value="">Tümü</option>
              <option value="pending">Beklemede</option>
              <option value="confirmed">Onaylandı</option>
              <option value="in_progress">İşlemde</option>
              <option value="completed">Tamamlandı</option>
              <option value="cancelled">İptal Edildi</option>
              <option value="no_show">No Show</option>
              <option value="failed">Başarısız</option>
            </select>
          </div>
          <div>
            <label htmlFor="fromDate" className="block text-sm font-medium text-gray-700">
              Başlangıç Tarihi
            </label>
            <input
              type="date"
              id="fromDate"
              name="fromDate"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              value={filters.fromDate}
              onChange={handleFilterChange}
            />
          </div>
          <div>
            <label htmlFor="toDate" className="block text-sm font-medium text-gray-700">
              Bitiş Tarihi
            </label>
            <input
              type="date"
              id="toDate"
              name="toDate"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              value={filters.toDate}
              onChange={handleFilterChange}
            />
          </div>
          <div className="md:col-span-2">
            <label htmlFor="searchTerm" className="block text-sm font-medium text-gray-700">
              Arama
            </label>
            <input
              type="text"
              id="searchTerm"
              name="searchTerm"
              placeholder="Rezervasyon no, müşteri adı, otel..."
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              value={filters.searchTerm}
              onChange={handleFilterChange}
            />
          </div>
          <div className="flex items-end">
            <button
              type="button"
              onClick={() => setFilters({
                status: '',
                agencyId: '',
                fromDate: '',
                toDate: '',
                searchTerm: '',
                page: 1,
                limit: 10
              })}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Filtreleri Temizle
            </button>
          </div>
        </div>
      </div>

      {/* Rezervasyon Listesi */}
      <div className="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
        {isLoading ? (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : reservations.length === 0 ? (
          <div className="text-center p-12">
            <p className="text-gray-500">Rezervasyon bulunamadı.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rezervasyon No
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acente
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nereden - Nereye
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tarih
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Yolcu Sayısı
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fiyat
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durum
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reservations.map((reservation) => (
                  <tr key={reservation.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {reservation.reservation_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reservation.agency?.name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reservation.fromRegion?.name || '-'} - {reservation.toRegion?.name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(reservation.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reservation.passenger_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(reservation.price, reservation.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => {
                          setSelectedReservation(reservation);
                          setShowStatusModal(true);
                        }}
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full hover:opacity-80 transition-opacity ${getStatusColor(reservation.status)}`}
                      >
                        {getStatusText(reservation.status)}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          to={`/reservations/${reservation.id}`}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          title="Detaylar"
                        >
                          <EyeIcon className="h-4 w-4" aria-hidden="true" />
                        </Link>
                        <Link
                          to={`/reservations/${reservation.id}/edit`}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          title="Düzenle"
                        >
                          <PencilIcon className="h-4 w-4" aria-hidden="true" />
                        </Link>
                        <Link
                          to={`/reservations/${reservation.id}/voucher`}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          title="Voucher"
                        >
                          <DocumentTextIcon className="h-4 w-4" aria-hidden="true" />
                        </Link>
                        <button
                          type="button"
                          onClick={() => handleCancelReservation(reservation.id)}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          title="İptal Et"
                        >
                          <TrashIcon className="h-4 w-4" aria-hidden="true" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Sayfalama */}
      {!isLoading && reservations.length > 0 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4 rounded-lg shadow">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                pagination.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              Önceki
            </button>
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage === pagination.totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                pagination.currentPage === pagination.totalPages ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              Sonraki
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Toplam <span className="font-medium">{pagination.total}</span> rezervasyondan{' '}
                <span className="font-medium">{(pagination.currentPage - 1) * pagination.limit + 1}</span>-
                <span className="font-medium">
                  {Math.min(pagination.currentPage * pagination.limit, pagination.total)}
                </span>{' '}
                arası gösteriliyor
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${
                    pagination.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <span className="sr-only">Önceki</span>
                  <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
                </button>
                {/* Sayfa numaraları */}
                {[...Array(pagination.totalPages).keys()].map((page) => (
                  <button
                    key={page + 1}
                    onClick={() => handlePageChange(page + 1)}
                    className={`relative inline-flex items-center px-4 py-2 border ${
                      pagination.currentPage === page + 1
                        ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    } text-sm font-medium`}
                  >
                    {page + 1}
                  </button>
                ))}
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${
                    pagination.currentPage === pagination.totalPages ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <span className="sr-only">Sonraki</span>
                  <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Status Update Modal */}
      {showStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Rezervasyon Durumu Güncelle</h3>
              <p className="text-sm text-gray-500 mb-4">
                Rezervasyon: {selectedReservation?.reservation_number}
              </p>
              <div className="space-y-3">
                {[
                  { value: 'pending', label: 'Beklemede', color: 'bg-yellow-100 text-yellow-800' },
                  { value: 'confirmed', label: 'Onaylandı', color: 'bg-green-100 text-green-800' },
                  { value: 'in_progress', label: 'Devam ediyor', color: 'bg-blue-100 text-blue-800' },
                  { value: 'completed', label: 'Tamamlandı', color: 'bg-purple-100 text-purple-800' },
                  { value: 'cancelled', label: 'İptal edildi', color: 'bg-red-100 text-red-800' },
                  { value: 'no_show', label: 'Gelmedi', color: 'bg-gray-100 text-gray-800' },
                  { value: 'failed', label: 'Başarısız', color: 'bg-red-100 text-red-800' }
                ].map(status => (
                  <button
                    key={status.value}
                    onClick={() => handleStatusChange(status.value)}
                    disabled={statusUpdateLoading}
                    className={`w-full text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 ${
                      selectedReservation?.status === status.value ? 'ring-2 ring-indigo-500 bg-indigo-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{status.label}</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${status.color}`}>
                        {status.label}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowStatusModal(false);
                    setSelectedReservation(null);
                  }}
                  disabled={statusUpdateLoading}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  İptal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReservationList;
