import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { transferService } from '../../services/transferService';
import { carrierService } from '../../services/carrierService';
import { vehicleService } from '../../services/vehicleService';
import { EyeIcon, PencilIcon, TruckIcon, UserGroupIcon } from '@heroicons/react/24/outline';

const TransferList = () => {
  const [transfers, setTransfers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [carriers, setCarriers] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [filters, setFilters] = useState({
    status: '',
    date: '',
    searchTerm: '',
    page: 1,
    limit: 10
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 10
  });

  // Quick action states
  const [quickActionLoading, setQuickActionLoading] = useState({});
  const [showCarrierModal, setShowCarrierModal] = useState(false);
  const [showVehicleModal, setShowVehicleModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState(null);

  useEffect(() => {
    fetchTransfers();
    fetchCarriers();
    fetchVehicles();
  }, [filters]);

  const fetchTransfers = async () => {
    setIsLoading(true);
    try {
      const response = await transferService.getAllTransfers(filters);

      if (response && response.success && response.data) {
        setTransfers(response.data.transfers || []);
        setPagination(response.data.pagination || {
          currentPage: parseInt(filters.page),
          totalPages: Math.ceil((response.data.total || 0) / filters.limit),
          total: response.data.total || 0,
          limit: parseInt(filters.limit)
        });
      } else {
        setTransfers([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          total: 0,
          limit: parseInt(filters.limit)
        });
      }
    } catch (error) {
      toast.error('Transferler yüklenirken bir hata oluştu');
      console.error('Error fetching transfers:', error);
      setTransfers([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCarriers = async () => {
    try {
      const response = await carrierService.getCarriers({ isActive: true });
      if (response?.success) {
        setCarriers(response.data?.rows || []);
      }
    } catch (error) {
      console.error('Error fetching carriers:', error);
    }
  };

  const fetchVehicles = async () => {
    try {
      const response = await vehicleService.getVehicles({ isActive: true });
      if (response?.success) {
        setVehicles(response.data?.vehicles || []);
      }
    } catch (error) {
      console.error('Error fetching vehicles:', error);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1 // Reset page when filter changes
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  // Quick Actions
  const handleStatusChange = async (transferId, newStatus) => {
    setQuickActionLoading(prev => ({ ...prev, [`status_${transferId}`]: true }));
    try {
      const response = await transferService.updateStatus(transferId, newStatus);
      if (response?.success) {
        setTransfers(prev => prev.map(transfer =>
          transfer.id === transferId ? { ...transfer, status: newStatus } : transfer
        ));
        toast.success('Durum başarıyla güncellendi');
        setShowStatusModal(false);
        setSelectedTransfer(null);
      } else {
        toast.error('Durum güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Durum güncellenirken bir hata oluştu');
    } finally {
      setQuickActionLoading(prev => ({ ...prev, [`status_${transferId}`]: false }));
    }
  };

  const handleCarrierAssign = async (carrierId) => {
    if (!selectedTransfer || !carrierId) return;

    setQuickActionLoading(prev => ({ ...prev, [`carrier_${selectedTransfer.id}`]: true }));
    try {
      const response = await transferService.assignCarrier(selectedTransfer.id, carrierId);
      if (response?.success) {
        setTransfers(prev => prev.map(transfer =>
          transfer.id === selectedTransfer.id
            ? { ...transfer, carrier_id: carrierId, carrier: carriers.find(c => c.id === parseInt(carrierId)) }
            : transfer
        ));
        toast.success('Taşıyıcı başarıyla atandı');
        setShowCarrierModal(false);
        setSelectedTransfer(null);
      } else {
        toast.error('Taşıyıcı atanırken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error assigning carrier:', error);
      toast.error('Taşıyıcı atanırken bir hata oluştu');
    } finally {
      setQuickActionLoading(prev => ({ ...prev, [`carrier_${selectedTransfer.id}`]: false }));
    }
  };

  const handleVehicleAssign = async (vehicleId) => {
    if (!selectedTransfer || !vehicleId) return;

    setQuickActionLoading(prev => ({ ...prev, [`vehicle_${selectedTransfer.id}`]: true }));
    try {
      const response = await transferService.assignVehicle(selectedTransfer.id, vehicleId);
      if (response?.success) {
        setTransfers(prev => prev.map(transfer =>
          transfer.id === selectedTransfer.id
            ? { ...transfer, vehicle_id: vehicleId, vehicle: vehicles.find(v => v.id === parseInt(vehicleId)) }
            : transfer
        ));
        toast.success('Araç başarıyla atandı');
        setShowVehicleModal(false);
        setSelectedTransfer(null);
      } else {
        toast.error('Araç atanırken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error assigning vehicle:', error);
      toast.error('Araç atanırken bir hata oluştu');
    } finally {
      setQuickActionLoading(prev => ({ ...prev, [`vehicle_${selectedTransfer.id}`]: false }));
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return '-';
    return timeString.substring(0, 5);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'assigned':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'in_progress':
        return 'Devam Ediyor';
      case 'assigned':
        return 'Atandı';
      case 'pending':
        return 'Beklemede';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return status;
    }
  };

  return (
    <div className="w-full max-w-none px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Transferler</h1>
          <p className="mt-2 text-sm text-gray-700">
            Tüm transfer işlemlerini görüntüleyin ve yönetin.
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6 bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Durum</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="">Tüm Durumlar</option>
              <option value="pending">Beklemede</option>
              <option value="assigned">Atandı</option>
              <option value="in_progress">Devam Ediyor</option>
              <option value="completed">Tamamlandı</option>
              <option value="cancelled">İptal Edildi</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Tarih</label>
            <input
              type="date"
              value={filters.date}
              onChange={(e) => handleFilterChange('date', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Arama</label>
            <input
              type="text"
              placeholder="Rezervasyon numarası, uçuş numarası..."
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Sayfa Başına</label>
            <select
              value={filters.limit}
              onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="mt-6 bg-white shadow rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
          </div>
        ) : transfers.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">Henüz transfer bulunmuyor.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rezervasyon
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rezervasyon Tarihi
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tip
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transfer Tarihi & Saati
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Uçuş
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Taşıyıcı
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Araç
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Durum
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hızlı İşlemler
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transfers.map((transfer) => (
                  <tr key={transfer.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {transfer.reservation?.reservation_number || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDateTime(transfer.reservation?.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transfer.is_return ? 'Dönüş' : 'Varış'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(transfer.transfer_date)} {formatTime(transfer.transfer_time)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transfer.flight_number || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transfer.carrier?.name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transfer.vehicle?.plate_number || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => {
                          setSelectedTransfer(transfer);
                          setShowStatusModal(true);
                        }}
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full hover:opacity-80 transition-opacity ${getStatusColor(transfer.status)}`}
                      >
                        {getStatusText(transfer.status)}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-1">
                        <button
                          onClick={() => {
                            setSelectedTransfer(transfer);
                            setShowCarrierModal(true);
                          }}
                          disabled={quickActionLoading[`carrier_${transfer.id}`]}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                          title="Taşıyıcı Ata"
                        >
                          <UserGroupIcon className="h-4 w-4" aria-hidden="true" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedTransfer(transfer);
                            setShowVehicleModal(true);
                          }}
                          disabled={quickActionLoading[`vehicle_${transfer.id}`]}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                          title="Araç Ata"
                        >
                          <TruckIcon className="h-4 w-4" aria-hidden="true" />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link
                          to={`/operations/transfers/${transfer.id}`}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          title="Detaylar"
                        >
                          <EyeIcon className="h-4 w-4" aria-hidden="true" />
                        </Link>
                        <Link
                          to={`/operations/transfers/${transfer.id}/edit`}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                          title="Düzenle"
                        >
                          <PencilIcon className="h-4 w-4" aria-hidden="true" />
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && transfers.length > 0 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4 rounded-lg shadow">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                pagination.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              Önceki
            </button>
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage === pagination.totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                pagination.currentPage === pagination.totalPages ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              Sonraki
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                <span className="font-medium">{((pagination.currentPage - 1) * pagination.limit) + 1}</span>
                {' - '}
                <span className="font-medium">
                  {Math.min(pagination.currentPage * pagination.limit, pagination.total)}
                </span>
                {' / '}
                <span className="font-medium">{pagination.total}</span>
                {' sonuç gösteriliyor'}
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  Önceki
                </button>
                
                {/* Page numbers */}
                {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const pageNumber = Math.max(1, pagination.currentPage - 2) + i;
                  if (pageNumber <= pagination.totalPages) {
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pageNumber === pagination.currentPage
                            ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  }
                  return null;
                })}
                
                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage === pagination.totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  Sonraki
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Carrier Assignment Modal */}
      {showCarrierModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Taşıyıcı Ata</h3>
              <p className="text-sm text-gray-500 mb-4">
                Transfer: {selectedTransfer?.reservation?.reservation_number} - {selectedTransfer?.is_return ? 'Dönüş' : 'Varış'}
              </p>
              <div className="space-y-3">
                {carriers.map(carrier => (
                  <button
                    key={carrier.id}
                    onClick={() => handleCarrierAssign(carrier.id)}
                    disabled={quickActionLoading[`carrier_${selectedTransfer?.id}`]}
                    className="w-full text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    <div className="font-medium">{carrier.name}</div>
                    <div className="text-sm text-gray-500">{carrier.phone}</div>
                  </button>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCarrierModal(false);
                    setSelectedTransfer(null);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  İptal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Vehicle Assignment Modal */}
      {showVehicleModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Araç Ata</h3>
              <p className="text-sm text-gray-500 mb-4">
                Transfer: {selectedTransfer?.reservation?.reservation_number} - {selectedTransfer?.is_return ? 'Dönüş' : 'Varış'}
              </p>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {vehicles.map(vehicle => (
                  <button
                    key={vehicle.id}
                    onClick={() => handleVehicleAssign(vehicle.id)}
                    disabled={quickActionLoading[`vehicle_${selectedTransfer?.id}`]}
                    className="w-full text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    <div className="font-medium">{vehicle.plate_number}</div>
                    <div className="text-sm text-gray-500">
                      {vehicle.vehicleType?.name} - Kapasite: {vehicle.capacity}
                    </div>
                    {vehicle.driver_name && (
                      <div className="text-sm text-gray-400">Şoför: {vehicle.driver_name}</div>
                    )}
                  </button>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowVehicleModal(false);
                    setSelectedTransfer(null);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  İptal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Update Modal */}
      {showStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Durum Güncelle</h3>
              <p className="text-sm text-gray-500 mb-4">
                Transfer: {selectedTransfer?.reservation?.reservation_number} - {selectedTransfer?.is_return ? 'Dönüş' : 'Varış'}
              </p>
              <div className="space-y-3">
                {[
                  { value: 'pending', label: 'Beklemede', color: 'bg-gray-100 text-gray-800' },
                  { value: 'assigned', label: 'Atanmış', color: 'bg-yellow-100 text-yellow-800' },
                  { value: 'in_progress', label: 'Devam ediyor', color: 'bg-blue-100 text-blue-800' },
                  { value: 'completed', label: 'Tamamlandı', color: 'bg-green-100 text-green-800' },
                  { value: 'cancelled', label: 'İptal edildi', color: 'bg-red-100 text-red-800' }
                ].map(status => (
                  <button
                    key={status.value}
                    onClick={() => handleStatusChange(selectedTransfer.id, status.value)}
                    disabled={quickActionLoading[`status_${selectedTransfer?.id}`]}
                    className={`w-full text-left p-3 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 ${
                      selectedTransfer?.status === status.value ? 'ring-2 ring-indigo-500 bg-indigo-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{status.label}</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${status.color}`}>
                        {status.label}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowStatusModal(false);
                    setSelectedTransfer(null);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  İptal
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransferList;
